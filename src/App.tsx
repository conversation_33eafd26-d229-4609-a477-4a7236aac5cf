import React, { useState } from 'react';
import { SubmissionForm } from './components/SubmissionForm';
import { SuccessPage } from './components/SuccessPage';
import { AdminPanel } from './components/AdminPanel';
import { SetupChecker } from './components/SetupChecker';
import { Button } from './components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './components/ui/card';
import { FileText, Users, BarChart3, Settings } from 'lucide-react';

type Page = 'setup' | 'home' | 'submit' | 'success' | 'admin';

export default function App() {
  const [currentPage, setCurrentPage] = useState<Page>('setup');
  const [submissionId, setSubmissionId] = useState<string>('');

  const handleSetupComplete = () => {
    setCurrentPage('home');
  };

  const handleSubmissionSuccess = (id: string) => {
    setSubmissionId(id);
    setCurrentPage('success');
  };

  const handleBackToSubmit = () => {
    setCurrentPage('submit');
  };

  if (currentPage === 'setup') {
    return <SetupChecker onSetupComplete={handleSetupComplete} />;
  }

  if (currentPage === 'submit') {
    return <SubmissionForm onSuccess={handleSubmissionSuccess} />;
  }

  if (currentPage === 'success') {
    return <SuccessPage submissionId={submissionId} onBackToSubmit={handleBackToSubmit} />;
  }

  if (currentPage === 'admin') {
    return <AdminPanel />;
  }

  // Home page
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="max-w-6xl mx-auto p-6 space-y-8">
        <div className="text-center space-y-4 py-12">
          <h1 className="text-5xl font-bold text-gray-900">
            STEMI AI Challenge
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Submit your AI healthcare project for evaluation using the ECGBench framework. 
            Compete on accuracy, speed, cost-effectiveness, and practical deployment.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={() => setCurrentPage('submit')}>
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <CardTitle>Submit Project</CardTitle>
                  <CardDescription>Upload your AI healthcare solution</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Submit your project through HTTP API, CLI tool, or N8N/chatbot interface. 
                Include documentation, performance metrics, and cost analysis.
              </p>
              <Button className="w-full">
                Start Submission
              </Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={() => setCurrentPage('admin')}>
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Users className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <CardTitle>Admin Panel</CardTitle>
                  <CardDescription>Secure access for reviewers</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Admin login required to review submissions, filter by criteria, 
                and export data in CSV or ECGBench format.
              </p>
              <Button variant="outline" className="w-full">
                Admin Login
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <BarChart3 className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <CardTitle>Evaluation Criteria</CardTitle>
                  <CardDescription>How projects are scored</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm">Accuracy</span>
                  <span className="text-sm font-medium">40%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Speed</span>
                  <span className="text-sm font-medium">20%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Cost</span>
                  <span className="text-sm font-medium">20%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Practicality</span>
                  <span className="text-sm font-medium">20%</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Submission Modes</CardTitle>
            <CardDescription>Choose the best way to showcase your AI solution</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center space-y-3">
                <div className="mx-auto w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Settings className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold">HTTP API</h3>
                <p className="text-sm text-gray-600">
                  Deploy your model as a REST API. Judges will send test data to your endpoint 
                  and measure response times and accuracy.
                </p>
              </div>
              
              <div className="text-center space-y-3">
                <div className="mx-auto w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <FileText className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold">CLI Tool</h3>
                <p className="text-sm text-gray-600">
                  Package your solution as a command-line tool. Include all dependencies 
                  and clear instructions for local execution.
                </p>
              </div>
              
              <div className="text-center space-y-3">
                <div className="mx-auto w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold">N8N/Chatbot</h3>
                <p className="text-sm text-gray-600">
                  Create an interactive interface using N8N workflows or chatbot platforms. 
                  Perfect for user-facing healthcare applications.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="text-center space-y-4 pt-8">
          <h2 className="text-2xl font-semibold">Ready to compete?</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Join the STEMI AI Challenge and showcase your innovative healthcare AI solution. 
            Get evaluated by industry experts using the standardized ECGBench framework.
          </p>
          <Button size="lg" onClick={() => setCurrentPage('submit')} className="px-8 py-3">
            Submit Your Project
          </Button>
        </div>
      </div>
    </div>
  );
}