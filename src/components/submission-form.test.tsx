import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { SubmissionForm } from './submission-form';
import * as api from '../utils/api';

// Mock the API module
vi.mock('../utils/api', () => ({
  submitProject: vi.fn(),
  uploadFileComplete: vi.fn()
}));

// Mock toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn()
  }
}));

describe('SubmissionForm', () => {
  const mockOnSuccess = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should submit form successfully', async () => {
    const mockSubmitProject = vi.mocked(api.submitProject);
    mockSubmitProject.mockResolvedValueOnce({
      success: true,
      submissionId: 'test-id-123'
    });

    render(<SubmissionForm onSuccess={mockOnSuccess} />);

    // Fill required fields
    fireEvent.change(screen.getByLabelText(/name/i), {
      target: { value: 'John <PERSON>e' }
    });
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    });
    fireEvent.change(screen.getByLabelText(/title/i), {
      target: { value: 'Test Project' }
    });

    // Submit form
    fireEvent.click(screen.getByRole('button', { name: /submit/i }));

    await waitFor(() => {
      expect(mockSubmitProject).toHaveBeenCalled();
      expect(mockOnSuccess).toHaveBeenCalledWith('test-id-123');
    });
  });

  it('should handle submission errors', async () => {
    const mockSubmitProject = vi.mocked(api.submitProject);
    mockSubmitProject.mockRejectedValueOnce(new Error('Submission failed'));

    render(<SubmissionForm onSuccess={mockOnSuccess} />);

    // Fill and submit form
    fireEvent.change(screen.getByLabelText(/name/i), {
      target: { value: 'John Doe' }
    });
    fireEvent.click(screen.getByRole('button', { name: /submit/i }));

    await waitFor(() => {
      expect(mockSubmitProject).toHaveBeenCalled();
      expect(mockOnSuccess).not.toHaveBeenCalled();
    });
  });
});