import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { CheckCircle, ExternalLink, FileText, Users } from 'lucide-react';

interface SuccessPageProps {
  submissionId: string;
  onBackToSubmit: () => void;
}

export function SuccessPage({ submissionId, onBackToSubmit }: SuccessPageProps) {
  return (
    <div className="max-w-2xl mx-auto p-6 space-y-8">
      <div className="text-center space-y-4">
        <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
          <CheckCircle className="h-8 w-8 text-green-600" />
        </div>
        <h1 className="text-3xl font-bold text-green-600">Submission Successful!</h1>
        <p className="text-muted-foreground">
          Your STEMI AI Challenge project has been successfully submitted for review.
        </p>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Submission Details</CardTitle>
          <CardDescription>Keep this information for your records</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Submission ID</p>
              <p className="font-mono text-sm bg-muted p-2 rounded">{submissionId}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Submitted At</p>
              <p className="text-sm">{new Date().toLocaleString()}</p>
            </div>
          </div>
          
          <div className="pt-4 border-t">
            <h4 className="font-medium mb-3">What happens next?</h4>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="mt-1">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                </div>
                <div>
                  <p className="text-sm font-medium">Initial Review</p>
                  <p className="text-sm text-muted-foreground">
                    Our review team will validate your submission and check all required files.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="mt-1">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                </div>
                <div>
                  <p className="text-sm font-medium">Technical Evaluation</p>
                  <p className="text-sm text-muted-foreground">
                    Judges will test your solution using the ECGBench evaluation framework.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="mt-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                </div>
                <div>
                  <p className="text-sm font-medium">Scoring & Results</p>
                  <p className="text-sm text-muted-foreground">
                    Final scores based on accuracy, speed, cost, and practicality will be calculated.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Important Reminders</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-start space-x-3">
            <Users className="h-5 w-5 mt-0.5 text-blue-500 flex-shrink-0" />
            <div>
              <p className="text-sm font-medium">Availability Window</p>
              <p className="text-sm text-muted-foreground">
                Ensure your API endpoints remain accessible during the evaluation period.
              </p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <FileText className="h-5 w-5 mt-0.5 text-blue-500 flex-shrink-0" />
            <div>
              <p className="text-sm font-medium">Documentation</p>
              <p className="text-sm text-muted-foreground">
                Clear README and documentation will help judges understand and evaluate your project effectively.
              </p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <ExternalLink className="h-5 w-5 mt-0.5 text-blue-500 flex-shrink-0" />
            <div>
              <p className="text-sm font-medium">Contact Information</p>
              <p className="text-sm text-muted-foreground">
                Keep your contact details updated in case judges need clarification.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <div className="flex justify-center space-x-4">
        <Button variant="outline" onClick={onBackToSubmit}>
          Submit Another Project
        </Button>
        <Button onClick={() => window.location.href = '/admin'}>
          View Admin Panel
        </Button>
      </div>
    </div>
  );
}