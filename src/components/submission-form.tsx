import React, { useState } from 'react';
import { useForm } from 'react-hook-form@7.55.0';
import { zodResolver } from '@hookform/resolvers/zod';
import { submissionSchema, type SubmissionFormData, type FileUpload } from '../types/submission';
import { uploadFileComplete, submitProject } from '../utils/api';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import { Label } from './ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Checkbox } from './ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Badge } from './ui/badge';
import { Progress } from './ui/progress';
import { AlertCircle, Upload, Check, Loader2 } from 'lucide-react';
import { Alert, AlertDescription } from './ui/alert';
import { toast } from 'sonner@2.0.3';

interface FileUploadFieldProps {
  name: string;
  label: string;
  accept?: string;
  required?: boolean;
  multiple?: boolean;
  value: FileUpload | FileUpload[] | undefined;
  onChange: (files: FileUpload | FileUpload[] | undefined) => void;
  error?: string;
}

function FileUploadField({ name, label, accept, required = false, multiple = false, value, onChange, error }: FileUploadFieldProps) {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;

    setUploading(true);
    setUploadProgress(0);

    try {
      if (multiple) {
        const uploads: FileUpload[] = [];
        for (let i = 0; i < files.length; i++) {
          const fileUpload = await uploadFileComplete(files[i]);
          uploads.push(fileUpload);
          setUploadProgress(((i + 1) / files.length) * 100);
        }
        onChange(uploads);
      } else {
        const fileUpload = await uploadFileComplete(files[0]);
        setUploadProgress(100);
        onChange(fileUpload);
      }
      toast.success('File(s) uploaded successfully');
    } catch (error) {
      console.error('Upload error:', error);
      toast.error('Failed to upload file(s)');
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const getDisplayValue = () => {
    if (!value) return null;
    if (Array.isArray(value)) {
      return value.map(f => f.filename).join(', ');
    }
    return value.filename;
  };

  return (
    <div className="space-y-2">
      <Label htmlFor={name}>
        {label} {required && <span className="text-red-500">*</span>}
      </Label>
      <div className="flex items-center space-x-2">
        <Input
          id={name}
          type="file"
          accept={accept}
          multiple={multiple}
          onChange={handleFileChange}
          disabled={uploading}
          className="flex-1"
        />
        {uploading && <Loader2 className="h-4 w-4 animate-spin" />}
        {value && !uploading && <Check className="h-4 w-4 text-green-500" />}
      </div>
      {uploading && (
        <Progress value={uploadProgress} className="w-full" />
      )}
      {value && (
        <p className="text-sm text-green-600">
          Uploaded: {getDisplayValue()}
        </p>
      )}
      {error && (
        <p className="text-sm text-red-500">{error}</p>
      )}
    </div>
  );
}

interface SubmissionFormProps {
  onSuccess: (submissionId: string) => void;
}

export function SubmissionForm({ onSuccess }: SubmissionFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<Record<string, FileUpload | FileUpload[]>>({});

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<SubmissionFormData>({
    resolver: zodResolver(submissionSchema),
    defaultValues: {
      mode: 'http',
      intendedUseEdu: true,
      ownershipOk: true,
      consentBench: true,
      consentPrivacy: true,
    },
  });

  const mode = watch('mode');

  const handleFileUpload = (fieldName: string, files: FileUpload | FileUpload[] | undefined) => {
    setUploadedFiles(prev => ({
      ...prev,
      [fieldName]: files
    }));
    setValue(fieldName as any, files);
  };

  const onSubmit = async (data: SubmissionFormData) => {
    setIsSubmitting(true);
    try {
      // Include uploaded files
      const submissionData = {
        ...data,
        ...uploadedFiles,
      };

      const result = await submitProject(submissionData);
      toast.success('Project submitted successfully!');
      onSuccess(result.submissionId);
    } catch (error) {
      console.error('Submission error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to submit project');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <div className="text-center">
        <h1 className="text-3xl mb-4">STEMI AI Challenge - Project Submission</h1>
        <p className="text-gray-600">Submit your AI project for evaluation and benchmarking</p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Student Information */}
        <Card>
          <CardHeader>
            <CardTitle>Student Information</CardTitle>
            <CardDescription>Basic information about you and your academic program</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Full Name *</Label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="John Doe"
                />
                {errors.name && <p className="text-sm text-red-500">{errors.name.message}</p>}
              </div>
              <div>
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  {...register('email')}
                  placeholder="<EMAIL>"
                />
                {errors.email && <p className="text-sm text-red-500">{errors.email.message}</p>}
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  {...register('phone')}
                  placeholder="+****************"
                />
              </div>
              <div>
                <Label htmlFor="program">Academic Program</Label>
                <Input
                  id="program"
                  {...register('program')}
                  placeholder="Computer Science, PhD"
                />
              </div>
              <div>
                <Label htmlFor="timezone">Timezone</Label>
                <Input
                  id="timezone"
                  {...register('timezone')}
                  placeholder="EST, PST, UTC+2"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Project Information */}
        <Card>
          <CardHeader>
            <CardTitle>Project Information</CardTitle>
            <CardDescription>Describe your AI project and its capabilities</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="title">Project Title *</Label>
              <Input
                id="title"
                {...register('title')}
                placeholder="ECG Arrhythmia Detection System"
              />
              {errors.title && <p className="text-sm text-red-500">{errors.title.message}</p>}
            </div>
            <div>
              <Label htmlFor="shortDesc">Short Description * (10-280 characters)</Label>
              <Textarea
                id="shortDesc"
                {...register('shortDesc')}
                placeholder="A brief description of your project and its main functionality"
                maxLength={280}
              />
              {errors.shortDesc && <p className="text-sm text-red-500">{errors.shortDesc.message}</p>}
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="abstract">Abstract (Optional)</Label>
                <Textarea
                  id="abstract"
                  {...register('abstract')}
                  placeholder="Detailed technical abstract..."
                />
              </div>
              <div>
                <Label htmlFor="keywords">Keywords (Optional)</Label>
                <Input
                  id="keywords"
                  {...register('keywords')}
                  placeholder="ECG, ML, arrhythmia, healthcare"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submission Mode */}
        <Card>
          <CardHeader>
            <CardTitle>Submission Mode</CardTitle>
            <CardDescription>Choose how judges will access and test your project</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={mode} onValueChange={(value) => setValue('mode', value as any)}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="http">HTTP API</TabsTrigger>
                <TabsTrigger value="cli">CLI Tool</TabsTrigger>
                <TabsTrigger value="n8n">N8N/Chatbot</TabsTrigger>
              </TabsList>

              <TabsContent value="http" className="space-y-4 mt-6">
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Your API endpoint will be tested automatically by our benchmarking system.
                  </AlertDescription>
                </Alert>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="httpEndpoint">API Endpoint URL *</Label>
                    <Input
                      id="httpEndpoint"
                      {...register('httpEndpoint')}
                      placeholder="https://api.example.com/predict"
                    />
                    {errors.httpEndpoint && <p className="text-sm text-red-500">{errors.httpEndpoint.message}</p>}
                  </div>
                  <div>
                    <Label htmlFor="authHeader">Authorization Header</Label>
                    <Input
                      id="authHeader"
                      {...register('authHeader')}
                      placeholder="Bearer token or API key"
                      type="password"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="availabilityStart">Available From *</Label>
                    <Input
                      id="availabilityStart"
                      type="datetime-local"
                      {...register('availabilityStart')}
                    />
                  </div>
                  <div>
                    <Label htmlFor="availabilityEnd">Available Until *</Label>
                    <Input
                      id="availabilityEnd"
                      type="datetime-local"
                      {...register('availabilityEnd')}
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="threshold">Detection Threshold</Label>
                  <Input
                    id="threshold"
                    type="number"
                    step="0.01"
                    {...register('threshold')}
                    placeholder="0.5"
                  />
                </div>
                <FileUploadField
                  name="httpSampleJson"
                  label="Sample JSON Request"
                  accept=".json,.txt"
                  value={uploadedFiles.httpSampleJson as FileUpload}
                  onChange={(files) => handleFileUpload('httpSampleJson', files)}
                  error={errors.httpSampleJson?.message}
                />
              </TabsContent>

              <TabsContent value="cli" className="space-y-4 mt-6">
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Provide a command-line interface that judges can run locally for testing.
                  </AlertDescription>
                </Alert>
                <div>
                  <Label htmlFor="cliCommand">CLI Command *</Label>
                  <Input
                    id="cliCommand"
                    {...register('cliCommand')}
                    placeholder="python predict.py --input data.csv --output results.csv"
                  />
                  {errors.cliCommand && <p className="text-sm text-red-500">{errors.cliCommand.message}</p>}
                </div>
                <div>
                  <Label htmlFor="osDeps">OS Dependencies</Label>
                  <Textarea
                    id="osDeps"
                    {...register('osDeps')}
                    placeholder="Python 3.8+, numpy, pandas, torch"
                  />
                </div>
                <FileUploadField
                  name="cliZip"
                  label="CLI Package (ZIP) *"
                  accept=".zip,.tar.gz"
                  required
                  value={uploadedFiles.cliZip as FileUpload}
                  onChange={(files) => handleFileUpload('cliZip', files)}
                  error={errors.cliZip?.message}
                />
                <FileUploadField
                  name="stdoutSample"
                  label="Sample Output"
                  accept=".txt,.log"
                  value={uploadedFiles.stdoutSample as FileUpload}
                  onChange={(files) => handleFileUpload('stdoutSample', files)}
                />
              </TabsContent>

              <TabsContent value="n8n" className="space-y-4 mt-6">
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Provide either an N8N workflow or a chatbot interface for interactive testing.
                  </AlertDescription>
                </Alert>
                <div>
                  <Label htmlFor="botUrl">Chatbot/Interface URL</Label>
                  <Input
                    id="botUrl"
                    {...register('botUrl')}
                    placeholder="https://chat.example.com/bot"
                  />
                </div>
                <div>
                  <Label htmlFor="testNotes">Testing Instructions</Label>
                  <Textarea
                    id="testNotes"
                    {...register('testNotes')}
                    placeholder="How to interact with your bot or workflow..."
                  />
                </div>
                <div>
                  <Label htmlFor="tempCreds">Temporary Credentials</Label>
                  <Textarea
                    id="tempCreds"
                    {...register('tempCreds')}
                    placeholder="Login details for testing (will be handled securely)"
                  />
                </div>
                <FileUploadField
                  name="n8nJson"
                  label="N8N Workflow JSON"
                  accept=".json"
                  value={uploadedFiles.n8nJson as FileUpload}
                  onChange={(files) => handleFileUpload('n8nJson', files)}
                />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Environment & Performance */}
        <Card>
          <CardHeader>
            <CardTitle>Environment & Performance</CardTitle>
            <CardDescription>Technical specifications and performance metrics</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="envProvider">Environment Provider *</Label>
                <Input
                  id="envProvider"
                  {...register('envProvider')}
                  placeholder="AWS, GCP, Azure, Local"
                />
                {errors.envProvider && <p className="text-sm text-red-500">{errors.envProvider.message}</p>}
              </div>
              <div>
                <Label htmlFor="specCpu">CPU Specification *</Label>
                <Input
                  id="specCpu"
                  {...register('specCpu')}
                  placeholder="2 vCPU, Intel i7"
                />
                {errors.specCpu && <p className="text-sm text-red-500">{errors.specCpu.message}</p>}
              </div>
              <div>
                <Label htmlFor="specRamGb">RAM (GB) *</Label>
                <Input
                  id="specRamGb"
                  {...register('specRamGb')}
                  placeholder="8"
                />
                {errors.specRamGb && <p className="text-sm text-red-500">{errors.specRamGb.message}</p>}
              </div>
              <div>
                <Label htmlFor="specGpu">GPU Specification</Label>
                <Input
                  id="specGpu"
                  {...register('specGpu')}
                  placeholder="NVIDIA V100, none"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="latencyP50Ms">Latency P50 (ms)</Label>
                <Input
                  id="latencyP50Ms"
                  type="number"
                  {...register('latencyP50Ms')}
                  placeholder="150"
                />
              </div>
              <div>
                <Label htmlFor="latencyP95Ms">Latency P95 (ms)</Label>
                <Input
                  id="latencyP95Ms"
                  type="number"
                  {...register('latencyP95Ms')}
                  placeholder="300"
                />
              </div>
              <div>
                <Label htmlFor="coldStartS">Cold Start (seconds)</Label>
                <Input
                  id="coldStartS"
                  type="number"
                  step="0.1"
                  {...register('coldStartS')}
                  placeholder="2.5"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="throughputEpm">Throughput (req/min)</Label>
                <Input
                  id="throughputEpm"
                  type="number"
                  {...register('throughputEpm')}
                  placeholder="60"
                />
              </div>
              <div>
                <Label htmlFor="costPer1k">Cost per 1000 requests ($) *</Label>
                <Input
                  id="costPer1k"
                  type="number"
                  step="0.01"
                  {...register('costPer1k')}
                  placeholder="0.50"
                />
                {errors.costPer1k && <p className="text-sm text-red-500">{errors.costPer1k.message}</p>}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Model Card */}
        <Card>
          <CardHeader>
            <CardTitle>Model Card</CardTitle>
            <CardDescription>Technical details about your AI model</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="inputTypes">Input Types *</Label>
                <Textarea
                  id="inputTypes"
                  {...register('inputTypes')}
                  placeholder="ECG signals (12-lead, 10-second windows), JSON format"
                />
                {errors.inputTypes && <p className="text-sm text-red-500">{errors.inputTypes.message}</p>}
              </div>
              <div>
                <Label htmlFor="datasets">Training Datasets *</Label>
                <Textarea
                  id="datasets"
                  {...register('datasets')}
                  placeholder="MIT-BIH Arrhythmia Database, PTB-XL, custom dataset"
                />
                {errors.datasets && <p className="text-sm text-red-500">{errors.datasets.message}</p>}
              </div>
            </div>
            <div>
              <Label htmlFor="architecture">Model Architecture *</Label>
              <Textarea
                id="architecture"
                {...register('architecture')}
                placeholder="ResNet-based CNN with attention mechanism, ensemble of 3 models"
              />
              {errors.architecture && <p className="text-sm text-red-500">{errors.architecture.message}</p>}
            </div>
            <div>
              <Label htmlFor="limitations">Known Limitations *</Label>
              <Textarea
                id="limitations"
                {...register('limitations')}
                placeholder="Performance may degrade with noisy signals, limited to specific ECG formats"
              />
              {errors.limitations && <p className="text-sm text-red-500">{errors.limitations.message}</p>}
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="intendedUseEdu"
                {...register('intendedUseEdu')}
              />
              <Label htmlFor="intendedUseEdu">
                This model is intended for educational/research use only *
              </Label>
            </div>
            {errors.intendedUseEdu && <p className="text-sm text-red-500">{errors.intendedUseEdu.message}</p>}
          </CardContent>
        </Card>

        {/* Safety & Compliance */}
        <Card>
          <CardHeader>
            <CardTitle>Safety & Compliance</CardTitle>
            <CardDescription>Important safety and privacy considerations</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="disclaimer">Safety Disclaimer *</Label>
              <Textarea
                id="disclaimer"
                {...register('disclaimer')}
                placeholder="This system is for research purposes only and should not be used for clinical diagnosis"
              />
              {errors.disclaimer && <p className="text-sm text-red-500">{errors.disclaimer.message}</p>}
            </div>
            <div>
              <Label htmlFor="phiHandling">PHI/Sensitive Data Handling *</Label>
              <Textarea
                id="phiHandling"
                {...register('phiHandling')}
                placeholder="No PHI is processed; all data is anonymized; HIPAA compliance measures"
              />
              {errors.phiHandling && <p className="text-sm text-red-500">{errors.phiHandling.message}</p>}
            </div>
            <div>
              <Label htmlFor="auditNotes">Audit Notes</Label>
              <Textarea
                id="auditNotes"
                {...register('auditNotes')}
                placeholder="Additional compliance or security notes for reviewers"
              />
            </div>
          </CardContent>
        </Card>

        {/* Artifacts */}
        <Card>
          <CardHeader>
            <CardTitle>Required Artifacts</CardTitle>
            <CardDescription>Upload supporting documentation and files</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <FileUploadField
              name="readme"
              label="README Documentation *"
              accept=".md,.txt,.pdf"
              required
              value={uploadedFiles.readme as FileUpload}
              onChange={(files) => handleFileUpload('readme', files)}
              error={errors.readme?.message}
            />
            <FileUploadField
              name="screenshots"
              label="Screenshots/Demos"
              accept="image/*"
              multiple
              value={uploadedFiles.screenshots as FileUpload[]}
              onChange={(files) => handleFileUpload('screenshots', files)}
            />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FileUploadField
                name="costSheet"
                label="Cost Analysis Sheet"
                accept=".xlsx,.csv,.pdf"
                value={uploadedFiles.costSheet as FileUpload}
                onChange={(files) => handleFileUpload('costSheet', files)}
              />
              <div>
                <Label htmlFor="demoVideoUrl">Demo Video URL</Label>
                <Input
                  id="demoVideoUrl"
                  {...register('demoVideoUrl')}
                  placeholder="https://youtube.com/watch?v=..."
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FileUploadField
                name="postmanCollection"
                label="Postman Collection"
                accept=".json"
                value={uploadedFiles.postmanCollection as FileUpload}
                onChange={(files) => handleFileUpload('postmanCollection', files)}
              />
              <FileUploadField
                name="dockerFile"
                label="Dockerfile"
                accept=".dockerfile,Dockerfile,.txt"
                value={uploadedFiles.dockerFile as FileUpload}
                onChange={(files) => handleFileUpload('dockerFile', files)}
              />
            </div>
          </CardContent>
        </Card>

        {/* GitHub Information */}
        <Card>
          <CardHeader>
            <CardTitle>GitHub Repository (Optional)</CardTitle>
            <CardDescription>Link to your project repository for additional context</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="githubUrl">GitHub Repository URL</Label>
              <Input
                id="githubUrl"
                {...register('githubUrl')}
                placeholder="https://github.com/username/project"
              />
              {errors.githubUrl && <p className="text-sm text-red-500">{errors.githubUrl.message}</p>}
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="githubVisibility">Repository Visibility</Label>
                <Select onValueChange={(value) => setValue('githubVisibility', value as any)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select visibility" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">Public</SelectItem>
                    <SelectItem value="private">Private</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="githubReviewers">Reviewer Access</Label>
                <Input
                  id="githubReviewers"
                  {...register('githubReviewers')}
                  placeholder="<EMAIL>, <EMAIL>"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="githubRef">Branch/Tag</Label>
                <Input
                  id="githubRef"
                  {...register('githubRef')}
                  placeholder="main, v1.0, submission"
                />
              </div>
              <div>
                <Label htmlFor="githubLicense">License</Label>
                <Input
                  id="githubLicense"
                  {...register('githubLicense')}
                  placeholder="MIT, Apache-2.0, GPL-3.0"
                />
              </div>
              <div className="flex items-center space-x-2 pt-6">
                <Checkbox
                  id="githubNoSecrets"
                  {...register('githubNoSecrets')}
                />
                <Label htmlFor="githubNoSecrets">No secrets in repo</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Legal Attestations */}
        <Card>
          <CardHeader>
            <CardTitle>Legal Attestations</CardTitle>
            <CardDescription>Required legal confirmations</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="ownershipOk"
                  {...register('ownershipOk')}
                />
                <Label htmlFor="ownershipOk">
                  I confirm that I own or have permission to use all content in this submission *
                </Label>
              </div>
              {errors.ownershipOk && <p className="text-sm text-red-500">{errors.ownershipOk.message}</p>}

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="consentBench"
                  {...register('consentBench')}
                />
                <Label htmlFor="consentBench">
                  I consent to my project being benchmarked and evaluated by the judging system *
                </Label>
              </div>
              {errors.consentBench && <p className="text-sm text-red-500">{errors.consentBench.message}</p>}

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="consentPrivacy"
                  {...register('consentPrivacy')}
                />
                <Label htmlFor="consentPrivacy">
                  I agree to the privacy policy and terms of the STEMI AI Challenge *
                </Label>
              </div>
              {errors.consentPrivacy && <p className="text-sm text-red-500">{errors.consentPrivacy.message}</p>}
            </div>

            <div>
              <Label htmlFor="eSignature">Electronic Signature *</Label>
              <Input
                id="eSignature"
                {...register('eSignature')}
                placeholder="Type your full name as electronic signature"
              />
              {errors.eSignature && <p className="text-sm text-red-500">{errors.eSignature.message}</p>}
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-center">
          <Button 
            type="submit" 
            disabled={isSubmitting}
            size="lg"
            className="w-full md:w-auto"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Submitting Project...
              </>
            ) : (
              'Submit Project'
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}