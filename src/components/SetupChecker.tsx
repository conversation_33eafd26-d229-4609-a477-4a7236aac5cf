import React, { useEffect, useState } from 'react';
import { Alert, AlertDescription } from './ui/alert';
import { Button } from './ui/button';
import { CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { projectId, publicAnonKey } from '../utils/supabase/info';

interface SetupCheckerProps {
  onSetupComplete: () => void;
}

export function SetupChecker({ onSetupComplete }: SetupCheckerProps) {
  const [isChecking, setIsChecking] = useState(true);
  const [setupStatus, setSetupStatus] = useState<'pending' | 'success' | 'error'>('pending');
  const [error, setError] = useState<string | null>(null);

  const initializeSetup = async () => {
    setIsChecking(true);
    setError(null);
    
    try {
      const response = await fetch(`https://${projectId}.supabase.co/functions/v1/make-server-8892caa8/setup`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${publicAnonKey}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to initialize database setup');
      }
      
      const result = await response.json();
      setSetupStatus('success');
      setTimeout(() => {
        onSetupComplete();
      }, 1500);
    } catch (error) {
      console.error('Setup error:', error);
      setError(error.message);
      setSetupStatus('error');
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    initializeSetup();
  }, []);

  if (setupStatus === 'success') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-emerald-100">
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <h2 className="text-2xl font-semibold text-green-800">Setup Complete!</h2>
          <p className="text-green-600">Database and storage initialized successfully.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="max-w-md w-full p-6 space-y-6">
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold">STEMI AI Challenge</h1>
          <p className="text-muted-foreground">Initializing submission portal...</p>
        </div>
        
        {isChecking && (
          <div className="text-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin mx-auto" />
            <p className="text-sm text-muted-foreground">Setting up database and storage...</p>
          </div>
        )}
        
        {setupStatus === 'error' && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error || 'Failed to initialize setup. Please try again.'}
            </AlertDescription>
          </Alert>
        )}
        
        {setupStatus === 'error' && (
          <Button onClick={initializeSetup} disabled={isChecking} className="w-full">
            {isChecking ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Retrying...
              </>
            ) : (
              'Retry Setup'
            )}
          </Button>
        )}
      </div>
    </div>
  );
}