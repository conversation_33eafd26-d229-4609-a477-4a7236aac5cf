import React, { useState } from 'react';
import { useForm } from 'react-hook-form@7.55.0';
import { zodResolver } from '@hookform/resolvers/zod';
import { submissionSchema, SubmissionFormData, FileUploadResult } from '../types/submission';
import { But<PERSON> } from './ui/button';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Checkbox } from './ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Alert, AlertDescription } from './ui/alert';
import { Upload, FileText, CheckCircle, AlertCircle } from 'lucide-react';
import { projectId, publicAnon<PERSON>ey } from '../utils/supabase/info';

interface SubmissionFormProps {
  onSuccess: (submissionId: string) => void;
}

export function SubmissionForm({ onSuccess }: SubmissionFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, boolean>>({});
  
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<SubmissionFormData>({
    resolver: zodResolver(submissionSchema),
    defaultValues: {
      intendedUseEdu: true,
      ownershipOk: true,
      consentBench: true,
      consentPrivacy: true,
    }
  });
  
  const mode = watch('mode');
  
  const uploadFile = async (file: File, fieldName: string): Promise<string> => {
    setUploadProgress(prev => ({ ...prev, [fieldName]: true }));
    
    try {
      // Get signed upload URL
      const signResponse = await fetch(`https://${projectId}.supabase.co/functions/v1/make-server-8892caa8/sign-upload`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${publicAnonKey}`
        },
        body: JSON.stringify({
          filename: file.name,
          contentType: file.type
        })
      });
      
      if (!signResponse.ok) {
        throw new Error('Failed to get upload URL');
      }
      
      const { uploadUrl, fileKey } = await signResponse.json();
      
      // Upload file to Supabase Storage
      const uploadResponse = await fetch(uploadUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type
        }
      });
      
      if (!uploadResponse.ok) {
        throw new Error('Failed to upload file');
      }
      
      return fileKey;
    } finally {
      setUploadProgress(prev => ({ ...prev, [fieldName]: false }));
    }
  };
  
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>, fieldName: keyof SubmissionFormData) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    try {
      const fileKey = await uploadFile(file, fieldName);
      setValue(fieldName as any, fileKey);
    } catch (error) {
      console.error('Upload error:', error);
      alert('Failed to upload file. Please try again.');
    }
  };
  
  const onSubmit = async (data: SubmissionFormData) => {
    setIsSubmitting(true);
    
    try {
      const response = await fetch(`https://${projectId}.supabase.co/functions/v1/make-server-8892caa8/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${publicAnonKey}`
        },
        body: JSON.stringify(data)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit');
      }
      
      const result = await response.json();
      onSuccess(result.submissionId);
    } catch (error) {
      console.error('Submission error:', error);
      alert(`Submission failed: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">STEMI AI Challenge - Project Submission</h1>
        <p className="text-muted-foreground">Submit your AI healthcare project for evaluation</p>
      </div>
      
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        <Tabs defaultValue="student" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="student">Student Info</TabsTrigger>
            <TabsTrigger value="project">Project</TabsTrigger>
            <TabsTrigger value="mode">Submission Mode</TabsTrigger>
            <TabsTrigger value="documentation">Documentation</TabsTrigger>
            <TabsTrigger value="attestation">Attestation</TabsTrigger>
          </TabsList>
          
          <TabsContent value="student" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Student Information</CardTitle>
                <CardDescription>Your personal and academic details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Full Name *</Label>
                    <Input {...register('name')} placeholder="John Doe" />
                    {errors.name && <p className="text-sm text-red-500">{errors.name.message}</p>}
                  </div>
                  <div>
                    <Label htmlFor="email">Email *</Label>
                    <Input {...register('email')} type="email" placeholder="<EMAIL>" />
                    {errors.email && <p className="text-sm text-red-500">{errors.email.message}</p>}
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="phone">Phone</Label>
                    <Input {...register('phone')} placeholder="+1234567890" />
                  </div>
                  <div>
                    <Label htmlFor="program">Program/School</Label>
                    <Input {...register('program')} placeholder="Computer Science" />
                  </div>
                  <div>
                    <Label htmlFor="timezone">Timezone</Label>
                    <Input {...register('timezone')} placeholder="UTC-5" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="project" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Project Information</CardTitle>
                <CardDescription>Details about your AI healthcare project</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="title">Project Title *</Label>
                  <Input {...register('title')} placeholder="ECG Arrhythmia Detection System" />
                  {errors.title && <p className="text-sm text-red-500">{errors.title.message}</p>}
                </div>
                
                <div>
                  <Label htmlFor="shortDesc">Short Description (10-280 chars) *</Label>
                  <Textarea {...register('shortDesc')} placeholder="Brief description of your project..." rows={3} />
                  {errors.shortDesc && <p className="text-sm text-red-500">{errors.shortDesc.message}</p>}
                </div>
                
                <div>
                  <Label htmlFor="abstract">Abstract (optional)</Label>
                  <Textarea {...register('abstract')} placeholder="Detailed technical abstract..." rows={4} />
                </div>
                
                <div>
                  <Label htmlFor="keywords">Keywords (optional)</Label>
                  <Input {...register('keywords')} placeholder="ECG, arrhythmia, deep learning, healthcare" />
                </div>
                
                <div>
                  <Label htmlFor="githubUrl">GitHub Repository (optional)</Label>
                  <Input {...register('githubUrl')} placeholder="https://github.com/username/project" />
                  {errors.githubUrl && <p className="text-sm text-red-500">{errors.githubUrl.message}</p>}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="mode" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Submission Mode</CardTitle>
                <CardDescription>Choose how judges will access your project</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="mode">Mode *</Label>
                  <Select onValueChange={(value) => setValue('mode', value as any)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select submission mode" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="http">HTTP API Endpoint</SelectItem>
                      <SelectItem value="cli">Command Line Interface</SelectItem>
                      <SelectItem value="n8n">N8N/Chatbot Interface</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.mode && <p className="text-sm text-red-500">{errors.mode.message}</p>}
                </div>
                
                {mode === 'http' && (
                  <div className="space-y-4 p-4 border rounded-lg">
                    <h4 className="font-medium">HTTP API Configuration</h4>
                    <div>
                      <Label htmlFor="httpEndpoint">API Endpoint *</Label>
                      <Input {...register('httpEndpoint')} placeholder="https://api.yourproject.com/predict" />
                    </div>
                    <div>
                      <Label htmlFor="authHeader">Authorization Header</Label>
                      <Input {...register('authHeader')} placeholder="Bearer your-token" />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="availabilityStart">Available From *</Label>
                        <Input {...register('availabilityStart')} type="datetime-local" />
                      </div>
                      <div>
                        <Label htmlFor="availabilityEnd">Available Until *</Label>
                        <Input {...register('availabilityEnd')} type="datetime-local" />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="httpSampleJson">Sample JSON Input *</Label>
                      <input
                        type="file"
                        accept=".json"
                        onChange={(e) => handleFileUpload(e, 'httpSampleJsonKey')}
                        className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                      />
                      {uploadProgress.httpSampleJsonKey && <p className="text-sm text-blue-500">Uploading...</p>}
                    </div>
                  </div>
                )}
                
                {mode === 'cli' && (
                  <div className="space-y-4 p-4 border rounded-lg">
                    <h4 className="font-medium">CLI Configuration</h4>
                    <div>
                      <Label htmlFor="cliCommand">Command to Run *</Label>
                      <Input {...register('cliCommand')} placeholder="python predict.py --input data.json" />
                    </div>
                    <div>
                      <Label htmlFor="osDeps">OS Dependencies</Label>
                      <Textarea {...register('osDeps')} placeholder="python3, numpy, torch..." rows={2} />
                    </div>
                    <div>
                      <Label htmlFor="cliZip">Project ZIP File *</Label>
                      <input
                        type="file"
                        accept=".zip"
                        onChange={(e) => handleFileUpload(e, 'cliZipKey')}
                        className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                      />
                      {uploadProgress.cliZipKey && <p className="text-sm text-blue-500">Uploading...</p>}
                    </div>
                  </div>
                )}
                
                {mode === 'n8n' && (
                  <div className="space-y-4 p-4 border rounded-lg">
                    <h4 className="font-medium">N8N/Chatbot Configuration</h4>
                    <div>
                      <Label htmlFor="botUrl">Bot/Interface URL *</Label>
                      <Input {...register('botUrl')} placeholder="https://your-bot.example.com" />
                    </div>
                    <div>
                      <Label htmlFor="testNotes">Testing Instructions</Label>
                      <Textarea {...register('testNotes')} placeholder="How to test your bot..." rows={3} />
                    </div>
                    <div>
                      <Label htmlFor="n8nJson">N8N Workflow (optional)</Label>
                      <input
                        type="file"
                        accept=".json"
                        onChange={(e) => handleFileUpload(e, 'n8nJsonKey')}
                        className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                      />
                      {uploadProgress.n8nJsonKey && <p className="text-sm text-blue-500">Uploading...</p>}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          

          
          <TabsContent value="documentation" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Documentation & Artifacts</CardTitle>
                <CardDescription>Upload supporting materials for your submission</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="readme">README File *</Label>
                  <input
                    type="file"
                    accept=".md,.txt,.pdf"
                    onChange={(e) => handleFileUpload(e, 'readmeKey')}
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                  />
                  {uploadProgress.readmeKey && <p className="text-sm text-blue-500">Uploading...</p>}
                  {errors.readmeKey && <p className="text-sm text-red-500">{errors.readmeKey.message}</p>}
                </div>
                
                <div>
                  <Label htmlFor="costSheet">Cost Analysis Sheet (optional)</Label>
                  <input
                    type="file"
                    accept=".xlsx,.csv,.pdf"
                    onChange={(e) => handleFileUpload(e, 'costSheetKey')}
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                  />
                  {uploadProgress.costSheetKey && <p className="text-sm text-blue-500">Uploading...</p>}
                </div>
                
                <div>
                  <Label htmlFor="demoVideoUrl">Demo Video URL (optional)</Label>
                  <Input {...register('demoVideoUrl')} placeholder="https://youtube.com/watch?v=..." />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="attestation" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Attestation & Consent</CardTitle>
                <CardDescription>Required confirmations and electronic signature</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox {...register('intendedUseEdu')} />
                    <Label>This project is intended for educational use only *</Label>
                  </div>
                  {errors.intendedUseEdu && <p className="text-sm text-red-500">{errors.intendedUseEdu.message}</p>}
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox {...register('ownershipOk')} />
                    <Label>I confirm ownership rights and authorization to submit this work *</Label>
                  </div>
                  {errors.ownershipOk && <p className="text-sm text-red-500">{errors.ownershipOk.message}</p>}
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox {...register('consentBench')} />
                    <Label>I consent to benchmarking and evaluation of this submission *</Label>
                  </div>
                  {errors.consentBench && <p className="text-sm text-red-500">{errors.consentBench.message}</p>}
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox {...register('consentPrivacy')} />
                    <Label>I agree to the privacy terms and data handling policies *</Label>
                  </div>
                  {errors.consentPrivacy && <p className="text-sm text-red-500">{errors.consentPrivacy.message}</p>}
                </div>
                
                <div>
                  <Label htmlFor="eSignature">Electronic Signature *</Label>
                  <Input {...register('eSignature')} placeholder="Type your full name as electronic signature" />
                  {errors.eSignature && <p className="text-sm text-red-500">{errors.eSignature.message}</p>}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
        
        <div className="flex justify-center">
          <Button 
            type="submit" 
            size="lg" 
            disabled={isSubmitting}
            className="px-8"
          >
            {isSubmitting ? (
              <>
                <Upload className="mr-2 h-4 w-4 animate-spin" />
                Submitting...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Submit Project
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}