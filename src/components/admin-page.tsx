import React, { useState, useEffect } from 'react';
import { 
  Download, 
  Filter, 
  Search, 
  Eye, 
  Calendar,
  User,
  Code,
  FileText,
  ExternalLink,
  Loader2
} from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';
import { Badge } from './ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { ScrollArea } from './ui/scroll-area';
import { Separator } from './ui/separator';
import { toast } from 'sonner@2.0.3';
import { getSubmissions, exportCSV, exportECGBench, getDownloadUrl, downloadBlob } from '../utils/api';
import type { StoredSubmission } from '../types/submission';

interface SubmissionDetailsProps {
  submission: StoredSubmission;
}

function SubmissionDetails({ submission }: SubmissionDetailsProps) {
  const [downloadingFile, setDownloadingFile] = useState<string | null>(null);

  const handleFileDownload = async (fileKey: string, filename: string) => {
    setDownloadingFile(fileKey);
    try {
      const downloadUrl = await getDownloadUrl(fileKey);
      const response = await fetch(downloadUrl);
      const blob = await response.blob();
      downloadBlob(blob, filename);
      toast.success(`Downloaded ${filename}`);
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download file');
    } finally {
      setDownloadingFile(null);
    }
  };

  const formatFileUpload = (upload: any) => {
    if (!upload) return null;
    if (Array.isArray(upload)) {
      return upload.map((file, index) => (
        <div key={index} className="flex items-center justify-between p-2 border rounded">
          <span className="text-sm">{file.filename}</span>
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleFileDownload(file.fileKey, file.filename)}
            disabled={downloadingFile === file.fileKey}
          >
            {downloadingFile === file.fileKey ? (
              <Loader2 className="h-3 w-3 animate-spin" />
            ) : (
              <Download className="h-3 w-3" />
            )}
          </Button>
        </div>
      ));
    }
    return (
      <div className="flex items-center justify-between p-2 border rounded">
        <span className="text-sm">{upload.filename}</span>
        <Button
          size="sm"
          variant="outline"
          onClick={() => handleFileDownload(upload.fileKey, upload.filename)}
          disabled={downloadingFile === upload.fileKey}
        >
          {downloadingFile === upload.fileKey ? (
            <Loader2 className="h-3 w-3 animate-spin" />
          ) : (
            <Download className="h-3 w-3" />
          )}
        </Button>
      </div>
    );
  };

  return (
    <ScrollArea className="h-[80vh]">
      <div className="space-y-6 p-6">
        {/* Basic Info */}
        <div>
          <h3 className="text-lg font-semibold mb-3">Student Information</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div><strong>Name:</strong> {submission.name}</div>
            <div><strong>Email:</strong> {submission.email}</div>
            <div><strong>Phone:</strong> {submission.phone || 'N/A'}</div>
            <div><strong>Program:</strong> {submission.program || 'N/A'}</div>
            <div><strong>Timezone:</strong> {submission.timezone || 'N/A'}</div>
            <div><strong>Submitted:</strong> {new Date(submission.createdAt).toLocaleString()}</div>
          </div>
        </div>

        <Separator />

        {/* Project Info */}
        <div>
          <h3 className="text-lg font-semibold mb-3">Project Information</h3>
          <div className="space-y-3 text-sm">
            <div><strong>Title:</strong> {submission.title}</div>
            <div><strong>Description:</strong> {submission.shortDesc}</div>
            {submission.abstract && (
              <div><strong>Abstract:</strong> {submission.abstract}</div>
            )}
            {submission.keywords && (
              <div><strong>Keywords:</strong> {submission.keywords}</div>
            )}
            <div className="flex items-center space-x-2">
              <strong>Mode:</strong>
              <Badge variant={
                submission.mode === 'http' ? 'default' :
                submission.mode === 'cli' ? 'secondary' : 'outline'
              }>
                {submission.mode.toUpperCase()}
              </Badge>
            </div>
          </div>
        </div>

        <Separator />

        {/* Mode-specific Details */}
        <div>
          <h3 className="text-lg font-semibold mb-3">Implementation Details</h3>
          <div className="space-y-3 text-sm">
            {submission.mode === 'http' && (
              <>
                <div><strong>Endpoint:</strong> {submission.httpEndpoint}</div>
                <div><strong>Auth Header:</strong> {submission.authHeader ? '[HIDDEN]' : 'None'}</div>
                <div><strong>Available:</strong> {submission.availabilityStart} to {submission.availabilityEnd}</div>
                {submission.threshold && <div><strong>Threshold:</strong> {submission.threshold}</div>}
                {submission.httpSampleJson && (
                  <div>
                    <strong>Sample JSON:</strong>
                    <div className="mt-2">{formatFileUpload(submission.httpSampleJson)}</div>
                  </div>
                )}
              </>
            )}
            
            {submission.mode === 'cli' && (
              <>
                <div><strong>Command:</strong> <code className="bg-gray-100 px-2 py-1 rounded">{submission.cliCommand}</code></div>
                {submission.osDeps && <div><strong>Dependencies:</strong> {submission.osDeps}</div>}
                {submission.cliZip && (
                  <div>
                    <strong>CLI Package:</strong>
                    <div className="mt-2">{formatFileUpload(submission.cliZip)}</div>
                  </div>
                )}
                {submission.stdoutSample && (
                  <div>
                    <strong>Sample Output:</strong>
                    <div className="mt-2">{formatFileUpload(submission.stdoutSample)}</div>
                  </div>
                )}
              </>
            )}
            
            {submission.mode === 'n8n' && (
              <>
                {submission.botUrl && <div><strong>Bot URL:</strong> <a href={submission.botUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">{submission.botUrl}</a></div>}
                {submission.testNotes && <div><strong>Test Notes:</strong> {submission.testNotes}</div>}
                {submission.tempCreds && <div><strong>Temp Credentials:</strong> [PROVIDED]</div>}
                {submission.n8nJson && (
                  <div>
                    <strong>N8N Workflow:</strong>
                    <div className="mt-2">{formatFileUpload(submission.n8nJson)}</div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        <Separator />

        {/* Performance Specs */}
        <div>
          <h3 className="text-lg font-semibold mb-3">Performance & Environment</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div><strong>Provider:</strong> {submission.envProvider}</div>
            <div><strong>CPU:</strong> {submission.specCpu}</div>
            <div><strong>RAM:</strong> {submission.specRamGb} GB</div>
            <div><strong>GPU:</strong> {submission.specGpu || 'None'}</div>
            {submission.latencyP50Ms && <div><strong>Latency P50:</strong> {submission.latencyP50Ms}ms</div>}
            {submission.latencyP95Ms && <div><strong>Latency P95:</strong> {submission.latencyP95Ms}ms</div>}
            {submission.coldStartS && <div><strong>Cold Start:</strong> {submission.coldStartS}s</div>}
            {submission.throughputEpm && <div><strong>Throughput:</strong> {submission.throughputEpm} req/min</div>}
            <div><strong>Cost per 1k:</strong> ${submission.costPer1k}</div>
          </div>
        </div>

        <Separator />

        {/* Model Card */}
        <div>
          <h3 className="text-lg font-semibold mb-3">Model Card</h3>
          <div className="space-y-3 text-sm">
            <div><strong>Input Types:</strong> {submission.inputTypes}</div>
            <div><strong>Datasets:</strong> {submission.datasets}</div>
            <div><strong>Architecture:</strong> {submission.architecture}</div>
            <div><strong>Limitations:</strong> {submission.limitations}</div>
            <div><strong>Educational Use:</strong> {submission.intendedUseEdu ? 'Yes' : 'No'}</div>
          </div>
        </div>

        <Separator />

        {/* Safety & Compliance */}
        <div>
          <h3 className="text-lg font-semibold mb-3">Safety & Compliance</h3>
          <div className="space-y-3 text-sm">
            <div><strong>Disclaimer:</strong> {submission.disclaimer}</div>
            <div><strong>PHI Handling:</strong> {submission.phiHandling}</div>
            {submission.auditNotes && <div><strong>Audit Notes:</strong> {submission.auditNotes}</div>}
          </div>
        </div>

        <Separator />

        {/* Artifacts */}
        <div>
          <h3 className="text-lg font-semibold mb-3">Artifacts</h3>
          <div className="space-y-4">
            {submission.readme && (
              <div>
                <strong>README:</strong>
                <div className="mt-2">{formatFileUpload(submission.readme)}</div>
              </div>
            )}
            {submission.screenshots && submission.screenshots.length > 0 && (
              <div>
                <strong>Screenshots:</strong>
                <div className="mt-2 space-y-2">{formatFileUpload(submission.screenshots)}</div>
              </div>
            )}
            {submission.costSheet && (
              <div>
                <strong>Cost Sheet:</strong>
                <div className="mt-2">{formatFileUpload(submission.costSheet)}</div>
              </div>
            )}
            {submission.demoVideoUrl && (
              <div>
                <strong>Demo Video:</strong>
                <a href={submission.demoVideoUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline ml-2">
                  {submission.demoVideoUrl}
                </a>
              </div>
            )}
            {submission.postmanCollection && (
              <div>
                <strong>Postman Collection:</strong>
                <div className="mt-2">{formatFileUpload(submission.postmanCollection)}</div>
              </div>
            )}
            {submission.dockerFile && (
              <div>
                <strong>Dockerfile:</strong>
                <div className="mt-2">{formatFileUpload(submission.dockerFile)}</div>
              </div>
            )}
          </div>
        </div>

        {submission.githubUrl && (
          <>
            <Separator />
            <div>
              <h3 className="text-lg font-semibold mb-3">GitHub Repository</h3>
              <div className="space-y-2 text-sm">
                <div>
                  <strong>URL:</strong>
                  <a href={submission.githubUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline ml-2">
                    {submission.githubUrl}
                  </a>
                </div>
                {submission.githubVisibility && <div><strong>Visibility:</strong> {submission.githubVisibility}</div>}
                {submission.githubReviewers && <div><strong>Reviewers:</strong> {submission.githubReviewers}</div>}
                {submission.githubRef && <div><strong>Branch/Tag:</strong> {submission.githubRef}</div>}
                {submission.githubLicense && <div><strong>License:</strong> {submission.githubLicense}</div>}
                {submission.githubNoSecrets && <div><strong>No Secrets:</strong> Confirmed</div>}
              </div>
            </div>
          </>
        )}

        <Separator />

        {/* Legal */}
        <div>
          <h3 className="text-lg font-semibold mb-3">Legal Attestations</h3>
          <div className="space-y-2 text-sm">
            <div><strong>Ownership Confirmed:</strong> {submission.ownershipOk ? 'Yes' : 'No'}</div>
            <div><strong>Benchmarking Consent:</strong> {submission.consentBench ? 'Yes' : 'No'}</div>
            <div><strong>Privacy Consent:</strong> {submission.consentPrivacy ? 'Yes' : 'No'}</div>
            <div><strong>Electronic Signature:</strong> {submission.eSignature}</div>
          </div>
        </div>
      </div>
    </ScrollArea>
  );
}

export function AdminPage() {
  const [submissions, setSubmissions] = useState<StoredSubmission[]>([]);
  const [filteredSubmissions, setFilteredSubmissions] = useState<StoredSubmission[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [modeFilter, setModeFilter] = useState<string>('all');
  const [exporting, setExporting] = useState<string | null>(null);

  useEffect(() => {
    loadSubmissions();
  }, []);

  useEffect(() => {
    filterSubmissions();
  }, [submissions, searchTerm, modeFilter]);

  const loadSubmissions = async () => {
    try {
      const data = await getSubmissions();
      setSubmissions(data);
    } catch (error) {
      console.error('Failed to load submissions:', error);
      toast.error('Failed to load submissions');
    } finally {
      setLoading(false);
    }
  };

  const filterSubmissions = () => {
    let filtered = submissions;

    if (searchTerm) {
      filtered = filtered.filter(sub =>
        sub.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        sub.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        sub.title.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (modeFilter !== 'all') {
      filtered = filtered.filter(sub => sub.mode === modeFilter);
    }

    setFilteredSubmissions(filtered);
  };

  const handleExportCSV = async () => {
    setExporting('csv');
    try {
      const blob = await exportCSV();
      downloadBlob(blob, `submissions_${new Date().toISOString().split('T')[0]}.csv`);
      toast.success('CSV exported successfully');
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export CSV');
    } finally {
      setExporting(null);
    }
  };

  const handleExportECGBench = async () => {
    setExporting('ecgbench');
    try {
      const blob = await exportECGBench();
      downloadBlob(blob, `ecgbench_students_${new Date().toISOString().split('T')[0]}.csv`);
      toast.success('ECGBench format exported successfully');
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export ECGBench format');
    } finally {
      setExporting(null);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading submissions...</span>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      <div>
        <h1 className="text-3xl mb-2">STEMI AI Challenge - Admin Panel</h1>
        <p className="text-gray-600">Review and export project submissions</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Total Submissions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{submissions.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">HTTP APIs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {submissions.filter(s => s.mode === 'http').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">CLI Tools</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {submissions.filter(s => s.mode === 'cli').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">N8N/Chatbots</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {submissions.filter(s => s.mode === 'n8n').length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Submissions Management</CardTitle>
          <CardDescription>Filter, search, and export submission data</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 flex-1">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by name, email, or title..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={modeFilter} onValueChange={setModeFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Modes</SelectItem>
                  <SelectItem value="http">HTTP API</SelectItem>
                  <SelectItem value="cli">CLI Tool</SelectItem>
                  <SelectItem value="n8n">N8N/Chatbot</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex gap-2">
              <Button
                onClick={handleExportCSV}
                disabled={exporting === 'csv'}
                variant="outline"
              >
                {exporting === 'csv' ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Download className="h-4 w-4 mr-2" />
                )}
                Export CSV
              </Button>
              <Button
                onClick={handleExportECGBench}
                disabled={exporting === 'ecgbench'}
              >
                {exporting === 'ecgbench' ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Download className="h-4 w-4 mr-2" />
                )}
                Export ECGBench
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submissions Table */}
      <Card>
        <CardHeader>
          <CardTitle>
            Submissions ({filteredSubmissions.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student</TableHead>
                <TableHead>Project Title</TableHead>
                <TableHead>Mode</TableHead>
                <TableHead>Cost per 1k</TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSubmissions.map((submission) => (
                <TableRow key={submission.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{submission.name}</div>
                      <div className="text-sm text-gray-600">{submission.email}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="max-w-xs">
                      <div className="font-medium truncate">{submission.title}</div>
                      <div className="text-sm text-gray-600 truncate">{submission.shortDesc}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={
                      submission.mode === 'http' ? 'default' :
                      submission.mode === 'cli' ? 'secondary' : 'outline'
                    }>
                      {submission.mode.toUpperCase()}
                    </Badge>
                  </TableCell>
                  <TableCell>${submission.costPer1k}</TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {new Date(submission.createdAt).toLocaleDateString()}
                    </div>
                    <div className="text-xs text-gray-600">
                      {new Date(submission.createdAt).toLocaleTimeString()}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-4xl">
                        <DialogHeader>
                          <DialogTitle>{submission.title}</DialogTitle>
                          <DialogDescription>
                            Submitted by {submission.name} on {new Date(submission.createdAt).toLocaleString()}
                          </DialogDescription>
                        </DialogHeader>
                        <SubmissionDetails submission={submission} />
                      </DialogContent>
                    </Dialog>
                  </TableCell>
                </TableRow>
              ))}
              {filteredSubmissions.length === 0 && (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                    No submissions found matching your criteria
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}