import React from 'react';
import { Check<PERSON>ir<PERSON>, Copy, ExternalLink } from 'lucide-react';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { toast } from 'sonner@2.0.3';

interface SuccessPageProps {
  submissionId: string;
  onNewSubmission: () => void;
}

export function SuccessPage({ submissionId, onNewSubmission }: SuccessPageProps) {
  const copySubmissionId = () => {
    navigator.clipboard.writeText(submissionId);
    toast.success('Submission ID copied to clipboard');
  };

  return (
    <div className="max-w-2xl mx-auto text-center space-y-8">
      <div className="space-y-4">
        <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
        <h1 className="text-3xl text-green-600">Submission Successful!</h1>
        <p className="text-gray-600">
          Your STEMI AI Challenge project has been successfully submitted and is now in the review queue.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Submission Details</CardTitle>
          <CardDescription>
            Keep this information for your records
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <p className="font-medium">Submission ID</p>
              <p className="text-sm text-gray-600 font-mono">{submissionId}</p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={copySubmissionId}
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy
            </Button>
          </div>
          
          <div className="flex items-center justify-center space-x-2">
            <Badge variant="secondary">Status: Submitted</Badge>
            <Badge variant="outline">Timestamp: {new Date().toLocaleString()}</Badge>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>What Happens Next?</CardTitle>
        </CardHeader>
        <CardContent className="text-left space-y-4">
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                1
              </div>
              <div>
                <h4 className="font-medium">Initial Review</h4>
                <p className="text-sm text-gray-600">
                  Our system will perform initial validation checks on your submission.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                2
              </div>
              <div>
                <h4 className="font-medium">ECGBench Testing</h4>
                <p className="text-sm text-gray-600">
                  Your project will be tested using our ECGBench evaluation framework.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                3
              </div>
              <div>
                <h4 className="font-medium">Judge Evaluation</h4>
                <p className="text-sm text-gray-600">
                  Judges will review your project across multiple criteria including accuracy, efficiency, and innovation.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                4
              </div>
              <div>
                <h4 className="font-medium">Results & Feedback</h4>
                <p className="text-sm text-gray-600">
                  You'll receive detailed feedback and scores once the evaluation is complete.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Evaluation Criteria</CardTitle>
        </CardHeader>
        <CardContent className="text-left">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-blue-600">Technical Performance</h4>
              <ul className="text-sm text-gray-600 space-y-1 mt-2">
                <li>• Accuracy on ECG datasets</li>
                <li>• Processing speed & latency</li>
                <li>• Resource efficiency</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-green-600">Practical Considerations</h4>
              <ul className="text-sm text-gray-600 space-y-1 mt-2">
                <li>• Cost effectiveness</li>
                <li>• Deployment simplicity</li>
                <li>• Real-world applicability</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-purple-600">Innovation & Design</h4>
              <ul className="text-sm text-gray-600 space-y-1 mt-2">
                <li>• Novel approaches</li>
                <li>• User experience</li>
                <li>• Technical creativity</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-orange-600">Documentation & Safety</h4>
              <ul className="text-sm text-gray-600 space-y-1 mt-2">
                <li>• Code quality & docs</li>
                <li>• Safety considerations</li>
                <li>• Reproducibility</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="space-y-4">
        <div className="text-sm text-gray-600">
          <p>
            Questions about your submission? Contact the organizers at{' '}
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
              <EMAIL>
            </a>
          </p>
          <p className="mt-2">
            Include your submission ID ({submissionId}) in any correspondence.
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            onClick={onNewSubmission}
            variant="outline"
          >
            Submit Another Project
          </Button>
          <Button asChild>
            <a 
              href="https://github.com/stemi-ai/challenge" 
              target="_blank" 
              rel="noopener noreferrer"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              View Challenge Details
            </a>
          </Button>
        </div>
      </div>
    </div>
  );
}