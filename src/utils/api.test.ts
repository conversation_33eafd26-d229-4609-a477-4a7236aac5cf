import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { submitProject } from './api';
import type { SubmissionFormData } from '../types/submission';

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('submitProject', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  const mockSubmissionData: SubmissionFormData = {
    name: '<PERSON>',
    email: '<EMAIL>',
    title: 'Test AI Project',
    shortDesc: 'A test project for unit testing',
    mode: 'http',
    httpEndpoint: 'https://api.example.com/predict',
    envProvider: 'AWS',
    specCpu: '2 vCPU',
    specRamGb: '4GB',
    costPer1k: 0.5,
    disclaimer: 'Test disclaimer',
    phiHandling: 'No PHI handled',
    readme: {
      fileKey: 'test-readme.md',
      filename: 'readme.md',
      size: 1024,
      contentType: 'text/markdown'
    }
  };

  it('should submit project successfully', async () => {
    const mockResponse = {
      success: true,
      submissionId: 'test-submission-id-123'
    };

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(mockResponse)
    });

    const result = await submitProject(mockSubmissionData);

    expect(mockFetch).toHaveBeenCalledWith(
      expect.stringContaining('/submit'),
      expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
          'Authorization': expect.stringContaining('Bearer')
        }),
        body: JSON.stringify(mockSubmissionData)
      })
    );

    expect(result).toEqual(mockResponse);
  });

  it('should handle submission failure', async () => {
    const errorMessage = 'Validation failed';
    mockFetch.mockResolvedValueOnce({
      ok: false,
      json: () => Promise.resolve({ error: errorMessage })
    });

    await expect(submitProject(mockSubmissionData)).rejects.toThrow(errorMessage);
  });

  it('should handle network errors', async () => {
    mockFetch.mockRejectedValueOnce(new Error('Network error'));

    await expect(submitProject(mockSubmissionData)).rejects.toThrow('Network error');
  });
});