import { projectId, publicAnonKey } from './supabase/info';
import type { SubmissionFormData, StoredSubmission, FileUpload } from '../types/submission';

const API_BASE = `https://${projectId}.supabase.co/functions/v1/make-server-8892caa8`;

const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${publicAnonKey}`,
};

export interface SignedUploadResponse {
  uploadUrl: string;
  fileKey: string;
  submissionId: string;
}

export interface SubmitResponse {
  success: boolean;
  submissionId: string;
}

// Get signed upload URL
export async function getSignedUploadUrl(filename: string, contentType: string): Promise<SignedUploadResponse> {
  const response = await fetch(`${API_BASE}/sign-upload`, {
    method: 'POST',
    headers,
    body: JSON.stringify({ filename, contentType }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to get upload URL');
  }

  return response.json();
}

// Upload file to signed URL
export async function uploadFile(file: File, uploadUrl: string): Promise<void> {
  const response = await fetch(uploadUrl, {
    method: 'PUT',
    body: file,
    headers: {
      'Content-Type': file.type,
    },
  });

  if (!response.ok) {
    throw new Error('Failed to upload file');
  }
}

// Upload file helper (combines getting signed URL and uploading)
export async function uploadFileComplete(file: File): Promise<FileUpload> {
  const { uploadUrl, fileKey, submissionId } = await getSignedUploadUrl(file.name, file.type);
  await uploadFile(file, uploadUrl);
  
  return {
    fileKey,
    filename: file.name,
    size: file.size,
    contentType: file.type,
  };
}

// Submit project
export async function submitProject(data: SubmissionFormData): Promise<SubmitResponse> {
  const response = await fetch(`${API_BASE}/submit`, {
    method: 'POST',
    headers,
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to submit project');
  }

  return response.json();
}

// Admin: Get all submissions
export async function getSubmissions(): Promise<StoredSubmission[]> {
  const response = await fetch(`${API_BASE}/admin/submissions`, {
    method: 'GET',
    headers,
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to fetch submissions');
  }

  return response.json();
}

// Admin: Export CSV
export async function exportCSV(): Promise<Blob> {
  const response = await fetch(`${API_BASE}/admin/export/csv`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${publicAnonKey}`,
    },
  });

  if (!response.ok) {
    throw new Error('Failed to export CSV');
  }

  return response.blob();
}

// Admin: Export ECGBench format
export async function exportECGBench(): Promise<Blob> {
  const response = await fetch(`${API_BASE}/admin/export/ecgbench`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${publicAnonKey}`,
    },
  });

  if (!response.ok) {
    throw new Error('Failed to export ECGBench format');
  }

  return response.blob();
}

// Get download URL for files
export async function getDownloadUrl(fileKey: string): Promise<string> {
  const response = await fetch(`${API_BASE}/get-download-url`, {
    method: 'POST',
    headers,
    body: JSON.stringify({ fileKey }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to get download URL');
  }

  const data = await response.json();
  return data.downloadUrl;
}

// Download helper
export function downloadBlob(blob: Blob, filename: string) {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}