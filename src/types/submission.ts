import { z } from 'zod';

// File upload schema
export const fileUploadSchema = z.object({
  fileKey: z.string(),
  filename: z.string(),
  size: z.number().positive(),
  contentType: z.string(),
});

// Base submission schema
export const submissionSchema = z.object({
  // Student Information
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  phone: z.string().optional(),
  program: z.string().optional(),
  timezone: z.string().optional(),

  // Project Information
  title: z.string().min(3, "Title must be at least 3 characters"),
  shortDesc: z.string().min(10, "Short description must be at least 10 characters").max(280, "Short description must be less than 280 characters"),
  abstract: z.string().optional(),
  keywords: z.string().optional(),

  // Mode Selection
  mode: z.enum(['http', 'cli', 'n8n'], {
    required_error: "Please select a submission mode"
  }),

  // HTTP Mode Fields
  httpEndpoint: z.string().url().optional(),
  authHeader: z.string().optional(),
  availabilityStart: z.string().optional(),
  availabilityEnd: z.string().optional(),
  httpSampleJson: fileUploadSchema.optional(),
  threshold: z.coerce.number().positive().optional(),

  // CLI Mode Fields
  cliCommand: z.string().optional(),
  osDeps: z.string().optional(),
  cliZip: fileUploadSchema.optional(),
  stdoutSample: fileUploadSchema.optional(),

  // N8N/Chatbot Mode Fields
  n8nJson: fileUploadSchema.optional(),
  botUrl: z.string().url().optional(),
  testNotes: z.string().optional(),
  tempCreds: z.string().optional(),

  // Environment & Performance
  envProvider: z.string().min(2, "Environment provider is required"),
  specCpu: z.string().min(1, "CPU specification is required"),
  specRamGb: z.string().min(1, "RAM specification is required"),
  specGpu: z.string().optional(),
  latencyP50Ms: z.coerce.number().int().positive().optional(),
  latencyP95Ms: z.coerce.number().int().positive().optional(),
  coldStartS: z.coerce.number().positive().optional(),
  throughputEpm: z.coerce.number().positive().optional(),
  costPer1k: z.coerce.number().positive("Cost per 1k must be a positive number"),

  // Model Card
  inputTypes: z.string().min(2, "Input types description is required"),
  datasets: z.string().min(2, "Datasets description is required"),
  architecture: z.string().min(2, "Architecture description is required"),
  limitations: z.string().min(2, "Limitations description is required"),
  intendedUseEdu: z.literal(true, {
    errorMap: () => ({ message: "You must confirm this is for educational use" })
  }),

  // Safety & Compliance
  disclaimer: z.string().min(2, "Disclaimer is required"),
  phiHandling: z.string().min(2, "PHI handling description is required"),
  auditNotes: z.string().optional(),

  // Artifacts
  readme: fileUploadSchema,
  screenshots: z.array(fileUploadSchema).optional(),
  costSheet: fileUploadSchema.optional(),
  demoVideoUrl: z.string().url().optional(),
  postmanCollection: fileUploadSchema.optional(),
  dockerFile: fileUploadSchema.optional(),

  // GitHub (Optional)
  githubUrl: z.string().url().regex(/^https:\/\/(www\.)?github\.com\/.+/, "Must be a valid GitHub URL").optional(),
  githubVisibility: z.enum(['public', 'private']).optional(),
  githubReviewers: z.string().optional(),
  githubRef: z.string().optional(),
  githubInferPath: z.string().optional(),
  githubLicense: z.string().optional(),
  githubNoSecrets: z.boolean().optional(),

  // Legal Attestations
  ownershipOk: z.literal(true, {
    errorMap: () => ({ message: "You must confirm ownership/permission for all content" })
  }),
  consentBench: z.literal(true, {
    errorMap: () => ({ message: "You must consent to benchmarking" })
  }),
  consentPrivacy: z.literal(true, {
    errorMap: () => ({ message: "You must consent to privacy policy" })
  }),
  eSignature: z.string().min(4, "Electronic signature must be at least 4 characters"),

  // Internal fields
  submissionId: z.string().optional(),
}).refine((data) => {
  // Mode-specific validations
  if (data.mode === 'http') {
    return data.httpEndpoint && data.availabilityStart && data.availabilityEnd;
  }
  if (data.mode === 'cli') {
    return data.cliCommand && data.cliZip;
  }
  if (data.mode === 'n8n') {
    return data.botUrl || data.n8nJson;
  }
  return true;
}, {
  message: "Please fill in all required fields for the selected mode",
  path: ["mode"]
});

export type SubmissionFormData = z.infer<typeof submissionSchema>;
export type FileUpload = z.infer<typeof fileUploadSchema>;

// Stored submission type (includes server-added fields)
export interface StoredSubmission extends SubmissionFormData {
  id: string;
  createdAt: string;
  status: 'submitted' | 'under_review' | 'approved' | 'rejected';
}

// ECGBench export format
export interface ECGBenchStudent {
  student_id: string;
  name: string;
  mode: string;
  endpoint_or_cmd: string;
  auth_header: string;
  cost_per_1000: number;
  footprint_points: number;
  deploy_simplicity_points: number;
  usability_points: number;
  workflow_points: number;
  innovation_points: number;
}