{"name": "<PERSON>", "email": "<EMAIL>", "phone": "******-0123", "program": "Computer Science - AI/ML Track", "timezone": "UTC-5", "title": "CardioAI: Real-time ECG Arrhythmia Detection", "shortDesc": "A deep learning system for detecting cardiac arrhythmias from 12-lead ECG signals in real-time with 95% accuracy.", "abstract": "This project implements a convolutional neural network combined with LSTM layers to analyze 12-lead ECG signals and detect various types of cardiac arrhythmias including atrial fibrillation, ventricular tachycardia, and premature ventricular contractions. The model was trained on a combination of MIT-BIH and PTB-XL datasets.", "keywords": "ECG, arrhythmia, deep learning, cardiology, real-time", "mode": "http", "httpEndpoint": "https://cardio-api.example.com/predict", "authHeader": "Bearer sk-cardio-ai-2024-demo-key", "availabilityStart": "2024-02-01T08:00:00", "availabilityEnd": "2024-02-28T23:59:59", "envProvider": "AWS ECS", "specCpu": "4 vCPU Intel Xeon", "specRamGb": "8GB", "specGpu": "NVIDIA T4, 16GB", "latencyP50Ms": 150, "latencyP95Ms": 300, "coldStartS": 2.5, "throughputEpm": 200, "costPer1k": 0.75, "inputTypes": "12-lead ECG signals, 500Hz sampling rate, 10-second segments in JSON format", "datasets": "MIT-BIH Arrhythmia Database (48 recordings), PTB-XL (21,837 10-second ECG recordings), Custom synthetic augmentation dataset", "architecture": "CNN-LSTM hybrid: 3 Conv1D layers (64, 128, 256 filters) + 2 LSTM layers (128 units) + Dense classification head", "limitations": "Performance degrades with heavily noise-corrupted signals (SNR < 5dB). Limited to common arrhythmia types. Not validated for pediatric patients.", "intendedUseEdu": true, "disclaimer": "This system is for educational and research purposes only. Not intended for clinical diagnosis or treatment decisions. Always consult healthcare professionals.", "phiHandling": "No PHI is stored or logged. All ECG data is processed in memory and immediately discarded after prediction. Timestamps and patient identifiers are stripped.", "auditNotes": "Complies with HIPAA technical safeguards. All API calls logged for security monitoring (no patient data in logs).", "readmeKey": "submissions/demo-readme-key", "screenshotsKeys": ["submissions/demo-screenshot-1", "submissions/demo-screenshot-2"], "costSheetKey": "submissions/demo-cost-analysis", "demoVideoUrl": "https://youtube.com/watch?v=demo-cardioai", "githubUrl": "https://github.com/alice-chen/cardio-ai-challenge", "githubVisibility": "public", "githubLicense": "MIT", "githubNoSecrets": true, "ownershipOk": true, "consentBench": true, "consentPrivacy": true, "eSignature": "<PERSON>"}