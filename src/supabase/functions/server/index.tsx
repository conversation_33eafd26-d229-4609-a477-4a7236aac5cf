import { Hono } from "npm:hono";
import { cors } from "npm:hono/cors";
import { logger } from "npm:hono/logger";
import { createClient } from "npm:@supabase/supabase-js";
import * as kv from "./kv_store.tsx";

const app = new Hono();

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Enable logger
app.use('*', logger(console.log));

// Enable CORS for all routes and methods
app.use(
  "/*",
  cors({
    origin: "*",
    allowHeaders: ["Content-Type", "Authorization", "x-client-info"],
    allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    exposeHeaders: ["Content-Length"],
    maxAge: 600,
  }),
);

// Health check endpoint
app.get("/make-server-8892caa8/health", (c) => {
  return c.json({ status: "ok", timestamp: new Date().toISOString() });
});

// Initialize storage and KV store
app.get("/make-server-8892caa8/setup", async (c) => {
  try {
    // Create storage bucket
    const { data: buckets } = await supabase.storage.listBuckets();
    const bucketExists = buckets?.some(bucket => bucket.name === 'submissions');
    
    if (!bucketExists) {
      const { error: bucketError } = await supabase.storage.createBucket('submissions', {
        public: false,
        fileSizeLimit: 52428800, // 50MB
      });
      
      if (bucketError) {
        console.error('Bucket creation error:', bucketError);
        return c.json({ error: 'Failed to create storage bucket', details: bucketError.message }, 500);
      }
    }

    // Initialize submissions index in KV store if it doesn't exist
    const existingIndex = await kv.get('submissions_index');
    if (!existingIndex) {
      await kv.set('submissions_index', []);
    }

    return c.json({ success: true, message: "Setup completed successfully" });
  } catch (error) {
    console.error('Setup error:', error);
    return c.json({ error: 'Setup failed', details: error.message }, 500);
  }
});

// Generate signed upload URL
app.post("/make-server-8892caa8/sign-upload", async (c) => {
  try {
    const { filename, contentType } = await c.req.json();
    
    if (!filename || !contentType) {
      return c.json({ error: 'Missing filename or contentType' }, 400);
    }
    
    // Generate unique file key
    const fileKey = `submissions/${crypto.randomUUID()}/${filename}`;
    
    // Create signed upload URL
    const { data, error } = await supabase.storage
      .from('submissions')
      .createSignedUploadUrl(fileKey, {
        expiresIn: 3600 // 1 hour
      });
    
    if (error) {
      console.error('Signed URL error:', error);
      return c.json({ error: 'Failed to create upload URL', details: error.message }, 500);
    }
    
    return c.json({
      uploadUrl: data.signedUrl,
      fileKey: fileKey,
      token: data.token
    });
  } catch (error) {
    console.error('Sign upload error:', error);
    return c.json({ error: 'Failed to process upload request', details: error.message }, 500);
  }
});

// Submit project
app.post("/make-server-8892caa8/submit", async (c) => {
  try {
    const submissionData = await c.req.json();
    
    // Generate unique submission ID
    const submissionId = crypto.randomUUID();
    const createdAt = new Date().toISOString();
    
    // Create submission record with all data
    const submission = {
      id: submissionId,
      createdAt: createdAt,
      ...submissionData
    };
    
    // Save submission to KV store
    await kv.set(`submission:${submissionId}`, submission);
    
    // Update submissions index
    const currentIndex = await kv.get('submissions_index') || [];
    currentIndex.push(submissionId);
    await kv.set('submissions_index', currentIndex);
    
    console.log('New submission created:', submissionId);
    
    return c.json({ 
      success: true, 
      submissionId: submissionId,
      message: 'Submission saved successfully' 
    });
  } catch (error) {
    console.error('Submit error:', error);
    return c.json({ error: 'Failed to process submission', details: error.message }, 500);
  }
});

// Get all submissions (admin)
app.get("/make-server-8892caa8/submissions", async (c) => {
  try {
    const submissionIds = await kv.get('submissions_index') || [];
    const submissions = [];
    
    // Fetch all submissions from KV store
    for (const id of submissionIds) {
      const submission = await kv.get(`submission:${id}`);
      if (submission) {
        submissions.push(submission);
      }
    }
    
    // Sort by creation date (newest first)
    submissions.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    
    return c.json({ submissions });
  } catch (error) {
    console.error('Get submissions error:', error);
    return c.json({ error: 'Failed to process request', details: error.message }, 500);
  }
});

// Export CSV
app.get("/make-server-8892caa8/export/csv", async (c) => {
  try {
    const submissionIds = await kv.get('submissions_index') || [];
    const submissions = [];
    
    // Fetch all submissions from KV store
    for (const id of submissionIds) {
      const submission = await kv.get(`submission:${id}`);
      if (submission) {
        submissions.push(submission);
      }
    }
    
    // Convert to CSV
    if (submissions.length === 0) {
      return new Response('', {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename="submissions.csv"'
        }
      });
    }
    
    const headers = Object.keys(submissions[0]).join(',');
    const rows = submissions.map(row => 
      Object.values(row).map(val => 
        typeof val === 'string' ? `"${val.replace(/"/g, '""')}"` : val
      ).join(',')
    );
    
    const csv = [headers, ...rows].join('\n');
    
    return new Response(csv, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename="submissions.csv"'
      }
    });
  } catch (error) {
    console.error('Export CSV error:', error);
    return c.json({ error: 'Failed to export CSV', details: error.message }, 500);
  }
});

// Export ECGBench format
app.get("/make-server-8892caa8/export/ecgbench", async (c) => {
  try {
    const submissionIds = await kv.get('submissions_index') || [];
    const submissions = [];
    
    // Fetch all submissions from KV store
    for (const id of submissionIds) {
      const submission = await kv.get(`submission:${id}`);
      if (submission) {
        submissions.push(submission);
      }
    }
    
    // Convert to ECGBench format
    const headers = 'student_id,name,mode,endpoint_or_cmd,auth_header,cost_per_1000,footprint_points,deploy_simplicity_points,usability_points,workflow_points,innovation_points';
    
    const rows = submissions.map(submission => {
      const endpointOrCmd = submission.mode === 'http' ? submission.httpEndpoint :
                           submission.mode === 'cli' ? submission.cliCommand :
                           submission.botUrl || '';
      
      return [
        `sub_${submission.id.slice(-8)}`,
        `"${submission.name}"`,
        submission.mode,
        `"${endpointOrCmd}"`,
        `"${submission.authHeader || ''}"`,
        submission.costPer1k,
        0, // footprint_points (to be filled by admin)
        0, // deploy_simplicity_points
        0, // usability_points  
        0, // workflow_points
        0  // innovation_points
      ].join(',');
    });
    
    const csv = [headers, ...rows].join('\n');
    
    return new Response(csv, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename="students.csv"'
      }
    });
  } catch (error) {
    console.error('Export ECGBench error:', error);
    return c.json({ error: 'Failed to export ECGBench format', details: error.message }, 500);
  }
});

Deno.serve(app.fetch);