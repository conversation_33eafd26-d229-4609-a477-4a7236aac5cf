import { Hono } from 'npm:hono';
import { cors } from 'npm:hono/cors';
import { logger } from 'npm:hono/logger';
import { createClient } from 'npm:@supabase/supabase-js@2';
import * as kv from './kv_store.tsx';

const app = new Hono();

// CORS configuration
app.use('*', cors({
  origin: '*',
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}));

app.use('*', logger(console.log));

// Initialize Supabase client
const supabase = createClient(
  Deno.env.get('SUPABASE_URL')!,
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
);

// Create submissions bucket on startup
const initStorage = async () => {
  const bucketName = 'submissions';
  const { data: buckets } = await supabase.storage.listBuckets();
  const bucketExists = buckets?.some(bucket => bucket.name === bucketName);
  if (!bucketExists) {
    const { error } = await supabase.storage.createBucket(bucketName, { public: false });
    if (error) {
      console.error('Error creating submissions bucket:', error);
    } else {
      console.log('Created submissions bucket');
    }
  }
};

// Initialize on startup
initStorage();

// Generate signed upload URL
app.post('/make-server-8892caa8/sign-upload', async (c) => {
  try {
    const { filename, contentType } = await c.req.json();
    
    if (!filename || !contentType) {
      return c.json({ error: 'Filename and contentType required' }, 400);
    }

    // Generate unique file key
    const submissionId = crypto.randomUUID();
    const fileKey = `submissions/${submissionId}/${filename}`;

    // Create signed URL for upload
    const { data, error } = await supabase.storage
      .from('submissions')
      .createSignedUploadUrl(fileKey, {
        upsert: true,
      });

    if (error) {
      console.error('Error creating signed upload URL:', error);
      return c.json({ error: 'Failed to create upload URL' }, 500);
    }

    return c.json({
      uploadUrl: data.signedUrl,
      fileKey: fileKey,
      submissionId: submissionId
    });
  } catch (error) {
    console.error('Error in sign-upload:', error);
    return c.json({ error: 'Internal server error' }, 500);
  }
});

// Submit project
app.post('/make-server-8892caa8/submit', async (c) => {
  try {
    const submissionData = await c.req.json();
    
    // Store submission data in KV store
    const submissionId = submissionData.submissionId || crypto.randomUUID();
    const timestamp = new Date().toISOString();
    
    const submission = {
      ...submissionData,
      id: submissionId,
      createdAt: timestamp,
      status: 'submitted'
    };

    await kv.set(`submission:${submissionId}`, submission);
    
    // Add to submissions index
    const existingIds = await kv.get('submission_ids') || [];
    existingIds.push(submissionId);
    await kv.set('submission_ids', existingIds);

    // Optional: Send webhook to n8n if configured
    const webhookUrl = Deno.env.get('N8N_WEBHOOK_URL');
    if (webhookUrl) {
      try {
        await fetch(webhookUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            submissionId,
            name: submission.name,
            email: submission.email,
            title: submission.title,
            mode: submission.mode,
            createdAt: timestamp
          })
        });
      } catch (webhookError) {
        console.error('Webhook error:', webhookError);
        // Don't fail the submission if webhook fails
      }
    }

    return c.json({ success: true, submissionId });
  } catch (error) {
    console.error('Error submitting project:', error);
    return c.json({ error: 'Failed to submit project' }, 500);
  }
});

// Get all submissions (admin)
app.get('/make-server-8892caa8/admin/submissions', async (c) => {
  try {
    const submissionIds = await kv.get('submission_ids') || [];
    const submissions = [];
    
    for (const id of submissionIds) {
      const submission = await kv.get(`submission:${id}`);
      if (submission) {
        submissions.push(submission);
      }
    }
    
    // Sort by creation date, newest first
    submissions.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    
    return c.json(submissions);
  } catch (error) {
    console.error('Error fetching submissions:', error);
    return c.json({ error: 'Failed to fetch submissions' }, 500);
  }
});

// Export submissions as CSV
app.get('/make-server-8892caa8/admin/export/csv', async (c) => {
  try {
    const submissionIds = await kv.get('submission_ids') || [];
    const submissions = [];
    
    for (const id of submissionIds) {
      const submission = await kv.get(`submission:${id}`);
      if (submission) {
        submissions.push(submission);
      }
    }

    // Generate CSV
    const headers = [
      'id', 'createdAt', 'name', 'email', 'phone', 'program', 'timezone',
      'title', 'shortDesc', 'abstract', 'keywords', 'mode',
      'httpEndpoint', 'authHeader', 'availabilityStart', 'availabilityEnd',
      'cliCommand', 'osDeps', 'botUrl', 'testNotes',
      'envProvider', 'specCpu', 'specRamGb', 'specGpu',
      'latencyP50Ms', 'latencyP95Ms', 'coldStartS', 'throughputEpm', 'costPer1k',
      'inputTypes', 'datasets', 'architecture', 'limitations', 'intendedUseEdu',
      'disclaimer', 'phiHandling', 'auditNotes',
      'githubUrl', 'githubVisibility', 'eSignature'
    ];

    const csvRows = [headers.join(',')];
    
    for (const submission of submissions) {
      const row = headers.map(header => {
        const value = submission[header] || '';
        // Escape quotes and wrap in quotes if contains comma
        const escaped = String(value).replace(/"/g, '""');
        return escaped.includes(',') ? `"${escaped}"` : escaped;
      });
      csvRows.push(row.join(','));
    }

    const csvContent = csvRows.join('\n');
    
    return new Response(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename="submissions.csv"'
      }
    });
  } catch (error) {
    console.error('Error exporting CSV:', error);
    return c.json({ error: 'Failed to export CSV' }, 500);
  }
});

// Export ECGBench format
app.get('/make-server-8892caa8/admin/export/ecgbench', async (c) => {
  try {
    const submissionIds = await kv.get('submission_ids') || [];
    const submissions = [];
    
    for (const id of submissionIds) {
      const submission = await kv.get(`submission:${id}`);
      if (submission) {
        submissions.push(submission);
      }
    }

    // ECGBench format headers
    const headers = [
      'student_id', 'name', 'mode', 'endpoint_or_cmd', 'auth_header', 'cost_per_1000',
      'footprint_points', 'deploy_simplicity_points', 'usability_points', 
      'workflow_points', 'innovation_points'
    ];

    const csvRows = [headers.join(',')];
    
    for (const submission of submissions) {
      const studentId = `sub_${submission.id.substring(0, 8)}`;
      const endpointOrCmd = submission.httpEndpoint || submission.cliCommand || submission.botUrl || '';
      
      const row = [
        studentId,
        submission.name || '',
        submission.mode || '',
        endpointOrCmd,
        submission.authHeader || '',
        submission.costPer1k || '0',
        '0', // footprint_points - to be scored manually
        '0', // deploy_simplicity_points - to be scored manually
        '0', // usability_points - to be scored manually
        '0', // workflow_points - to be scored manually
        '0'  // innovation_points - to be scored manually
      ];
      
      const escapedRow = row.map(value => {
        const escaped = String(value).replace(/"/g, '""');
        return escaped.includes(',') ? `"${escaped}"` : escaped;
      });
      
      csvRows.push(escapedRow.join(','));
    }

    const csvContent = csvRows.join('\n');
    
    return new Response(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename="ecgbench_students.csv"'
      }
    });
  } catch (error) {
    console.error('Error exporting ECGBench format:', error);
    return c.json({ error: 'Failed to export ECGBench format' }, 500);
  }
});

// Get signed download URL for files
app.post('/make-server-8892caa8/get-download-url', async (c) => {
  try {
    const { fileKey } = await c.req.json();
    
    if (!fileKey) {
      return c.json({ error: 'File key required' }, 400);
    }

    const { data, error } = await supabase.storage
      .from('submissions')
      .createSignedUrl(fileKey, 900); // 15 minutes

    if (error) {
      console.error('Error creating signed download URL:', error);
      return c.json({ error: 'Failed to create download URL' }, 500);
    }

    return c.json({ downloadUrl: data.signedUrl });
  } catch (error) {
    console.error('Error in get-download-url:', error);
    return c.json({ error: 'Internal server error' }, 500);
  }
});

export default app;