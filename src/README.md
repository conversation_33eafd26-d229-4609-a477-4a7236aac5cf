# STEMI AI Challenge - Project Submission Portal

A comprehensive web application for students to submit AI healthcare projects for the STEMI AI Challenge. Built with React, TypeScript, Tailwind CSS, and Supabase.

## Features

### 🎯 **Multi-Mode Submissions**
- **HTTP API**: Submit your model as a REST API endpoint
- **CLI Tool**: Package your solution as a command-line interface  
- **N8N/Chatbot**: Create interactive healthcare interfaces

### 📊 **Comprehensive Evaluation**
Projects are scored on:
- **Accuracy** (40%) - Clinical performance metrics
- **Speed** (20%) - Response time and latency
- **Cost** (20%) - Cost per 1000 requests
- **Practicality** (20%) - Deployment simplicity and usability

### 🔧 **Admin Features**
- Review all submissions in a clean interface
- Filter by submission mode and search capabilities
- Export data in CSV format for analysis
- Export ECGBench-compatible format for evaluation

### 🔒 **Security & Compliance**
- Secure file uploads to Supabase Storage
- No PHI handling in the submission process
- Electronic signatures for attestation
- Privacy consent management

## Getting Started

### 1. **Submit Your Project**
Click "Submit Project" from the home page and follow these steps:

**Student Information**
- Provide your contact details and academic program

**Project Details**
- Enter your project title and description
- Add GitHub repository (optional but recommended)

**Submission Mode**
Choose how judges will evaluate your project:

- **HTTP API Mode**: Provide your API endpoint, authentication, and availability window
- **CLI Mode**: Upload your packaged CLI tool with dependencies
- **N8N Mode**: Share your bot URL and testing instructions

**Performance Metrics**
- Environment specifications (CPU, RAM, GPU)
- Latency measurements (P50, P95)
- Cost analysis per 1000 requests
- Model card information (datasets, architecture, limitations)

**Documentation**
- Upload README file (required)
- Add screenshots, cost sheets, demo videos
- Include Postman collections or Docker configs

**Attestation**
- Confirm educational use and ownership rights
- Provide electronic signature

### 2. **File Upload Requirements**

**Required Files:**
- README.md or .txt (project documentation)
- Mode-specific files:
  - HTTP: Sample JSON input file
  - CLI: ZIP package with your tool
  - N8N: Workflow JSON file (optional)

**Optional Files:**
- Screenshots of your interface
- Cost analysis spreadsheet
- Postman collection
- Docker configuration

**File Limits:**
- Maximum 50MB per file
- Supported formats: .md, .txt, .pdf, .json, .zip, .xlsx, .csv

### 3. **Admin Panel**

Access the admin interface to:
- **View Submissions**: See all projects with key details
- **Filter & Search**: Find specific submissions by name, email, or project title
- **Export CSV**: Download complete submission data
- **Export ECGBench**: Generate students.csv for evaluation framework

### 4. **ECGBench Integration**

The portal generates ECGBench-compatible export with these columns:
- `student_id`: Unique identifier
- `name`: Student name
- `mode`: Submission type (http/cli/n8n)
- `endpoint_or_cmd`: API endpoint or CLI command
- `auth_header`: Authentication details
- `cost_per_1000`: Cost per 1000 requests
- Manual scoring columns (initially set to 0 for admin completion)

## Technical Architecture

### Frontend
- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **React Hook Form** with Zod validation
- **Shadcn/ui** component library

### Backend
- **Supabase** for storage and infrastructure
- **Hono** web framework for API routes
- **KV Store** for submission data persistence
- **Supabase Storage** for secure file handling

### Data Storage

The application uses a KV store for submission data with these key patterns:
- `submissions_index`: Array of all submission IDs
- `submission:{id}`: Individual submission records containing:
  - Student information (name, email, program)
  - Project details (title, description, GitHub)
  - Mode-specific configurations
  - Performance metrics and costs
  - Model card information
  - File references and URLs
  - Attestation records

## Security Features

### Data Protection
- All file uploads use signed URLs
- Private storage bucket with access controls
- No PHI (Personal Health Information) stored
- Electronic signatures for legal compliance

### File Security
- Type validation on uploads
- Size limits enforced (50MB max)
- Secure file key generation
- Temporary signed download URLs

## Evaluation Process

1. **Submission Validation**: Automated checks for required fields and files
2. **Technical Review**: Manual review of documentation and setup
3. **ECGBench Testing**: Automated evaluation using standardized framework
4. **Scoring**: Combined automated and manual scoring across four criteria
5. **Results**: Final rankings and detailed feedback

## API Endpoints

The application provides these server endpoints:

- `GET /health` - Health check
- `GET /setup` - Initialize database and storage
- `POST /sign-upload` - Generate signed upload URLs
- `POST /submit` - Save submission data
- `GET /submissions` - List all submissions (admin)
- `GET /export/csv` - Export CSV format
- `GET /export/ecgbench` - Export ECGBench format

## Deployment

The application is designed for deployment in the Figma Make environment with Supabase as the backend service. The setup includes:

- Automatic storage bucket creation
- KV store initialization for data persistence
- CORS configuration for web access
- Environment-based configuration

## Support

For technical issues or questions about the submission process, please contact the STEMI AI Challenge organizers.

---

**Ready to compete?** Click "Submit Project" to get started with your AI healthcare solution submission!